import { useState, useEffect } from "react";
import "./App.css";
import { AppService } from "./services/AppService";
import { GAME_MESSAGES } from "./utils/gameUtils";
import { playAudioWithFallback } from "./utils/audioUtils";
import { SimpleVoiceChat } from "./components/SimpleVoiceChat";

function App() {
  const [audioUrl, setAudioUrl] = useState<string>("");
  const [aiLoading, setAiLoading] = useState<boolean>(false);
  const [generatedCharacter, setGeneratedCharacter] = useState<string>("");
  const [gameStarted, setGameStarted] = useState<boolean>(false);
  const [initialMessage, setInitialMessage] = useState<string>("");
  const [characterError, setCharacterError] = useState<string>(""); // 👈 Para errores de generación

  const appService = AppService.getInstance();

  // Configurar el callback de audio en el servicio
  useEffect(() => {
    appService.setAudioCallback((audioUrl: string) => {
      setAudioUrl(audioUrl);
      // Intentar reproducir automáticamente
      setTimeout(() => {
        playAudioWithFallback(audioUrl);
      }, 100);
    });
  }, [appService]);

  // Funciones para el servicio de IA
  const handleGenerateCharacter = async () => {
    setAiLoading(true);
    setCharacterError(""); // Limpiar errores previos

    try {
      const response = await appService.generateWithGenCharBot(
        GAME_MESSAGES.GENERATE_CHARACTER
      );

      const characterResponse =
        response.response ||
        response.output ||
        response.result ||
        response.text ||
        response.content;

      if (characterResponse) {
        setGeneratedCharacter(characterResponse);
        console.log('✅ Personaje generado:', characterResponse);
      } else {
        // Debug fallback
        const responseText = JSON.stringify(response, null, 2);
        console.log('🔍 Debug - Respuesta completa:', responseText);

        const fallbackText = response.input || response.query || "Personaje de prueba";
        setGeneratedCharacter(fallbackText);
      }
    } catch (error) {
      console.error('❌ Error generando personaje:', error);
      setCharacterError("Error al generar el personaje. Por favor, inténtalo de nuevo.");
    } finally {
      setAiLoading(false);
    }
  };

  const handleStartGame = async () => {
    if (!generatedCharacter.trim()) {
      alert("Primero debes generar un personaje");
      return;
    }

    setAiLoading(true);
    setGameStarted(true);

    try {
      const response = await appService.generateWithIaVsPlayer(
        GAME_MESSAGES.INITIAL_GAME_QUERY,
        undefined, // id
        generatedCharacter // personaje
      );

      const responseText =
        response.response ||
        response.output ||
        response.result ||
        response.text ||
        response.content ||
        "Respuesta no encontrada";

      // Guardar mensaje inicial para que el componente lo use
      setInitialMessage(responseText);
      console.log('🎮 Juego iniciado con mensaje:', responseText);

    } catch (error) {
      console.error('❌ Error al iniciar el juego:', error);
      setInitialMessage("Error al iniciar el juego. Por favor, inténtalo de nuevo.");
    } finally {
      setAiLoading(false);
    }
  };

  // Nueva función que combina generación de personaje e inicio del juego
  const handleStartGameDirectly = async () => {
    setAiLoading(true);
    setCharacterError("");

    try {
      // Paso 1: Generar personaje
      console.log('🎲 Generando personaje automáticamente...');
      const characterResponse = await appService.generateWithGenCharBot(
        GAME_MESSAGES.GENERATE_CHARACTER
      );

      const characterText =
        characterResponse.response ||
        characterResponse.output ||
        characterResponse.result ||
        characterResponse.text ||
        characterResponse.content;

      if (!characterText) {
        throw new Error("No se pudo generar el personaje");
      }

      setGeneratedCharacter(characterText);
      console.log('✅ Personaje generado:', characterText);

      // Paso 2: Iniciar el juego inmediatamente
      console.log('🚀 Iniciando juego automáticamente...');
      setGameStarted(true);

      const gameResponse = await appService.generateWithIaVsPlayer(
        GAME_MESSAGES.INITIAL_GAME_QUERY,
        undefined, // id
        characterText // personaje
      );

      const responseText =
        gameResponse.response ||
        gameResponse.output ||
        gameResponse.result ||
        gameResponse.text ||
        gameResponse.content ||
        "Respuesta no encontrada";

      setInitialMessage(responseText);
      console.log('🎮 Juego iniciado con mensaje:', responseText);

    } catch (error) {
      console.error('❌ Error en inicio directo del juego:', error);
      setCharacterError("Error al generar personaje o iniciar juego. Inténtalo de nuevo.");
      setGameStarted(false);
      setGeneratedCharacter("");
    } finally {
      setAiLoading(false);
    }
  };

  const handleResetGame = () => {
    setGeneratedCharacter("");
    setGameStarted(false);
    setAudioUrl("");
    setInitialMessage("");
    setCharacterError("");
    console.log('🔄 Juego reiniciado');
  };

  return (
    <>
      <h1>Genigma WB - Juego de Adivinanza</h1>
      <div className="card">
        <div
          style={{
            marginTop: "30px",
            padding: "20px",
            border: "1px solid #ccc",
            borderRadius: "8px",
          }}
        >
          <h3>🎯 Juego de Adivinanza de Personajes</h3>

          {/* Botón principal: Iniciar Juego Directo */}
          {!gameStarted && (
            <div
              style={{
                marginBottom: "30px",
                padding: "20px",
                backgroundColor: "#e8f5e8",
                borderRadius: "12px",
                textAlign: "center",
                border: "2px solid #28a745",
              }}
            >
              <h4 style={{ color: "#155724", marginBottom: "15px" }}>
                🚀 Inicio Rápido
              </h4>
              <p style={{ color: "#155724", marginBottom: "20px" }}>
                ¡Comienza a jugar inmediatamente! Se generará un personaje automáticamente y comenzará el juego.
              </p>
              <button
                onClick={handleStartGameDirectly}
                disabled={aiLoading}
                style={{
                  padding: "15px 30px",
                  backgroundColor: aiLoading ? "#ccc" : "#28a745",
                  color: "white",
                  border: "none",
                  borderRadius: "8px",
                  cursor: aiLoading ? "not-allowed" : "pointer",
                  fontSize: "18px",
                  fontWeight: "bold",
                  boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                }}
              >
                {aiLoading ? "Iniciando Juego..." : "🎮 INICIAR JUEGO"}
              </button>
            </div>
          )}

          {/* Sección expandible para modo manual */}
          {!gameStarted && (
            <details style={{ marginBottom: "20px" }}>
              <summary style={{
                cursor: "pointer",
                padding: "10px",
                backgroundColor: "#f8f9fa",
                borderRadius: "8px",
                border: "1px solid #dee2e6",
                fontWeight: "bold",
                color: "#495057"
              }}>
                ⚙️ Modo Manual (Paso a Paso)
              </summary>

              {/* Paso 1: Generar Personaje */}
              <div
                style={{
                  marginTop: "15px",
                  marginBottom: "20px",
                  padding: "15px",
                  backgroundColor: "#f0f8ff",
                  borderRadius: "8px",
                }}
              >
                <h4 style={{ color: "#007bff" }}>Paso 1: Generar Personaje</h4>
                <p style={{ color: "#333" }}>
                  Primero, genera un personaje que la IA tendrá que adivinar:
                </p>

                <div style={{ marginBottom: "15px" }}>
                  <button
                    onClick={handleGenerateCharacter}
                    disabled={aiLoading}
                    style={{
                      padding: "10px 20px",
                      backgroundColor: aiLoading ? "#ccc" : "#28a745",
                      color: "white",
                      border: "none",
                      borderRadius: "4px",
                      cursor: aiLoading ? "not-allowed" : "pointer",
                      marginRight: "10px",
                    }}
                  >
                    {aiLoading ? "Generando..." : "🎲 Generar Personaje"}
                  </button>

                  {/* Botón de debug temporal */}
                  <button
                    onClick={() => {
                      setGeneratedCharacter("Personaje de prueba para debug");
                      setGameStarted(false);
                      setCharacterError("");
                    }}
                    style={{
                      padding: "10px 20px",
                      backgroundColor: "#ffc107",
                      color: "black",
                      border: "none",
                      borderRadius: "4px",
                      cursor: "pointer",
                    }}
                  >
                    🔧 Debug: Forzar Personaje
                  </button>
                </div>

                {/* Mostrar personaje generado */}
                {generatedCharacter && (
                  <div
                    style={{
                      padding: "10px",
                      color: "#155724",
                      backgroundColor: "#d4edda",
                      border: "1px solid #c3e6cb",
                      borderRadius: "4px",
                    }}
                  >
                    <strong>✅ Personaje generado:</strong> {generatedCharacter}
                  </div>
                )}

                {/* Mostrar errores */}
                {characterError && (
                  <div
                    style={{
                      padding: "10px",
                      color: "#721c24",
                      backgroundColor: "#f8d7da",
                      border: "1px solid #f5c6cb",
                      borderRadius: "4px",
                    }}
                  >
                    <strong>❌ Error:</strong> {characterError}
                  </div>
                )}
              </div>

              {/* Paso 2: Iniciar Juego */}
              {generatedCharacter && !gameStarted && (
                <div
                  style={{
                    marginBottom: "20px",
                    padding: "15px",
                    color: "black",
                    backgroundColor: "#fff3cd",
                    borderRadius: "8px",
                  }}
                >
                  <h4>Paso 2: Iniciar el Juego</h4>
                  <p>
                    Ahora inicia el juego donde la IA intentará adivinar tu personaje.
                    <strong> Se activará automáticamente la conversación por voz.</strong>
                  </p>
                  <button
                    onClick={handleStartGame}
                    disabled={aiLoading}
                    style={{
                      padding: "12px 24px",
                      backgroundColor: aiLoading ? "#ccc" : "#007bff",
                      color: "white",
                      border: "none",
                      borderRadius: "8px",
                      cursor: aiLoading ? "not-allowed" : "pointer",
                      fontSize: "16px",
                      fontWeight: "bold"
                    }}
                  >
                    {aiLoading ? "Iniciando..." : "🚀 Iniciar Juego con IA"}
                  </button>
                </div>
              )}
            </details>
          )}

          {/* Chat de voz simplificado - maneja todo el historial */}
          <SimpleVoiceChat
            generatedCharacter={generatedCharacter}
            isGameStarted={gameStarted}
            initialMessage={initialMessage}
          />

          {/* Botón para reiniciar */}
          {(generatedCharacter || gameStarted) && (
            <div style={{ marginTop: "20px", textAlign: "center" }}>
              <button
                onClick={handleResetGame}
                style={{
                  padding: "8px 16px",
                  backgroundColor: "#6c757d",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
              >
                🔄 Reiniciar Juego
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default App;
