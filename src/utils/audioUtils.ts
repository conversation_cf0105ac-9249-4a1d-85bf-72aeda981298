// Utilidades para manejo de audio

export const playAudioWithFallback = async (audioUrl: string): Promise<void> => {
  try {
    const audio = new Audio(audioUrl);
    audio.preload = 'auto';
    audio.volume = 0.8;

    // Mantener referencia para evitar garbage collection
    (window as any).currentAudio = audio;

    await audio.play();
    console.log('✅ Audio reproducido exitosamente');

    // Limpiar referencia cuando termine
    audio.addEventListener('ended', () => {
      (window as any).currentAudio = null;
    });

  } catch (error) {
    console.warn('⚠️ No se pudo reproducir automáticamente:', error);
  }
};

export const createAudioElement = (audioUrl: string, autoplay: boolean = true): HTMLAudioElement => {
  const audio = new Audio(audioUrl);
  audio.controls = true;
  audio.preload = 'auto';
  audio.volume = 0.8;

  if (autoplay) {
    // Intentar reproducir cuando esté listo
    audio.addEventListener('canplaythrough', () => {
      audio.play().catch(error => {
        console.warn('⚠️ Autoplay bloqueado:', error);
      });
    });
  }

  return audio;
};
